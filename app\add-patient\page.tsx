"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ProtectedLayout } from "@/components/protected-layout"
import { DiseaseBadgeSelector } from "@/components/disease-badge-selector"
import { dataClient } from "@/lib/data-client"
import { useToast } from "@/hooks/use-toast"

const patientSchema = z.object({
  patient_id: z.string().min(1, "Patient ID is required"),
  name: z.string().min(1, "Full name is required"),
  admission_date: z.string().min(1, "Admission date is required"),
  gender: z.enum(["Male", "Female"]),
  age: z.number().min(1).max(150),
  weight: z.number().positive(),
  unit: z.enum(["ICU A", "ICU B"]),
  main_complaint: z.string().min(1, "Main complaint is required"),
  medical_history: z.string().optional(),
  initial_diagnosis: z.string().min(1, "Initial diagnosis is required"),
})

type PatientFormData = z.infer<typeof patientSchema>

export default function AddPatientPage() {
  const [selectedDiseases, setSelectedDiseases] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      admission_date: new Date().toISOString().split("T")[0],
    },
  })

  const onSubmit = async (data: PatientFormData) => {
    setIsSubmitting(true)

    try {
      await dataClient.insertPatient({
        ...data,
        diseases: selectedDiseases,
      })

      toast({
        title: "Patient added",
        description: "Patient has been successfully added to the system.",
      })

      router.push("/")
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add patient. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <ProtectedLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          <h1 className="text-3xl font-bold">Add Patient</h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Patient Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input id="name" placeholder="patient's full name" {...register("name")} />
                {errors.name && <p className="text-sm text-red-600">{errors.name.message}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="patient_id">Patient ID *</Label>
                  <Input id="patient_id" placeholder="12345" {...register("patient_id")} />
                  {errors.patient_id && <p className="text-sm text-red-600">{errors.patient_id.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="admission_date">Admission Date</Label>
                  <Input id="admission_date" type="date" {...register("admission_date")} />
                  {errors.admission_date && <p className="text-sm text-red-600">{errors.admission_date.message}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender *</Label>
                  <Select onValueChange={(value) => setValue("gender", value as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Male">Male</SelectItem>
                      <SelectItem value="Female">Female</SelectItem>
                     
                    </SelectContent>
                  </Select>
                  {errors.gender && <p className="text-sm text-red-600">{errors.gender.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="age">Age *</Label>
                  <Input id="age" type="number" placeholder="age" {...register("age", { valueAsNumber: true })} />
                  {errors.age && <p className="text-sm text-red-600">{errors.age.message}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weight">Weight (kg) *</Label>
                  <Input
                    id="weight"
                    type="number"
                    step="0.1"
                    placeholder="weight"
                    {...register("weight", { valueAsNumber: true })}
                  />
                  {errors.weight && <p className="text-sm text-red-600">{errors.weight.message}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="unit">Unit *</Label>
                <Select onValueChange={(value) => setValue("unit", value as any)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ICU A">ICU A</SelectItem>
                    <SelectItem value="ICU B">ICU B</SelectItem>
                  </SelectContent>
                </Select>
                {errors.unit && <p className="text-sm text-red-600">{errors.unit.message}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="main_complaint">Main Complaint *</Label>
                <Textarea id="main_complaint" placeholder="Describe main complaint" {...register("main_complaint")} />
                {errors.main_complaint && <p className="text-sm text-red-600">{errors.main_complaint.message}</p>}
              </div>

              <div className="space-y-2">
                <Label>Chronic Disease History</Label>
                <DiseaseBadgeSelector selectedDiseases={selectedDiseases} onChange={setSelectedDiseases} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="medical_history">Additional Medical History</Label>
                <Textarea
                  id="medical_history"
                  placeholder="Additional medical history, medications, allergies, etc."
                  {...register("medical_history")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="initial_diagnosis">Initial Diagnosis *</Label>
                <Textarea id="initial_diagnosis" placeholder="initial diagnosis" {...register("initial_diagnosis")} />
                {errors.initial_diagnosis && <p className="text-sm text-red-600">{errors.initial_diagnosis.message}</p>}
              </div>

              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Adding Patient..." : "Add Patient"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </ProtectedLayout>
  )
}
