"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { X, Plus } from "lucide-react"

const DISEASE_CATEGORIES = {
  Cardiovascular: ["HTN", "CAD", "AF", "CHF", "MI"],
  Respiratory: ["COPD", "ASTH", "ARDS", "PE"],
  Endocrine: ["DM", "HTH", "ADTH"],
  Renal: ["CKD", "AKI", "ESRD"],
  Neurological: ["CVA", "SZ", "TBI"],
  Gastrointestinal: ["GERD", "IBD", "CLD"],
  Hematological: ["ANEMIA", "DVT", "BLEED"],
  Infectious: ["SEPSIS", "UTI", "PNA"],
  Other: ["CA", "ORTHO", "PSYCH"],
}

interface DiseaseBadgeSelectorProps {
  selectedDiseases: string[]
  onChange: (diseases: string[]) => void
}

export function DiseaseBadgeSelector({ selectedDiseases, onChange }: DiseaseBadgeSelectorProps) {
  const [customInput, setCustomInput] = useState("")

  const toggleDisease = (disease: string) => {
    if (selectedDiseases.includes(disease)) {
      onChange(selectedDiseases.filter((d) => d !== disease))
    } else {
      onChange([...selectedDiseases, disease])
    }
  }

  const addCustomDiseases = () => {
    if (!customInput.trim()) return

    const newDiseases = customInput
      .split(",")
      .map((d) => d.trim().toUpperCase())
      .filter((d) => d && !selectedDiseases.includes(d))

    onChange([...selectedDiseases, ...newDiseases])
    setCustomInput("")
  }

  const removeDisease = (disease: string) => {
    onChange(selectedDiseases.filter((d) => d !== disease))
  }

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        
        {(() => {
          // Pastel/tint styles per category (fallback) and disease groups to match the screenshot
          type StylePair = { base: string; selected: string }

          const categoryStyles: Record<string, StylePair> = {
            Cardiovascular: {
              base: "bg-red-50 text-red-800 border-red-200 hover:bg-red-100",
              selected: "bg-red-500 text-white border-red-500 hover:bg-red-600",
            },
            Respiratory: {
              base: "bg-emerald-50 text-emerald-800 border-emerald-200 hover:bg-emerald-100",
              selected: "bg-emerald-500 text-white border-emerald-500 hover:bg-emerald-600",
            },
            Endocrine: {
              base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100",
              selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600",
            },
            Renal: {
              base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100",
              selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600",
            },
            Neurological: {
              base: "bg-violet-50 text-violet-800 border-violet-200 hover:bg-violet-100",
              selected: "bg-violet-500 text-white border-violet-500 hover:bg-violet-600",
            },
            Gastrointestinal: {
              base: "bg-emerald-50 text-emerald-800 border-emerald-200 hover:bg-emerald-100",
              selected: "bg-emerald-500 text-white border-emerald-500 hover:bg-emerald-600",
            },
            Hematological: {
              base: "bg-rose-50 text-rose-800 border-rose-200 hover:bg-rose-100",
              selected: "bg-rose-500 text-white border-rose-500 hover:bg-rose-600",
            },
            Infectious: {
              base: "bg-orange-50 text-orange-800 border-orange-200 hover:bg-orange-100",
              selected: "bg-orange-500 text-white border-orange-500 hover:bg-orange-600",
            },
            Other: {
              base: "bg-zinc-50 text-zinc-700 border-zinc-200 hover:bg-zinc-100",
              selected: "bg-zinc-600 text-white border-zinc-600 hover:bg-zinc-700",
            },
          }

          // Disease-specific group overrides to match the provided screenshot exactly
          const diseaseStyles: Record<string, StylePair> = {
            // Cardiovascular group (red tints)
            HTN: categoryStyles.Cardiovascular,
            CHF: categoryStyles.Cardiovascular,
            CAD: categoryStyles.Cardiovascular,
            "OLD CVS": categoryStyles.Cardiovascular,
            AF: categoryStyles.Cardiovascular,
            CABG: categoryStyles.Cardiovascular,

            // Blue group
            DM: { base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100", selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600" },
            CKD: { base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100", selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600" },
            CLD: { base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100", selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600" },
            ESRD: { base: "bg-sky-50 text-sky-800 border-sky-200 hover:bg-sky-100", selected: "bg-sky-500 text-white border-sky-500 hover:bg-sky-600" },

            // Green group
            COPD: categoryStyles.Respiratory,
            ASTH: categoryStyles.Respiratory,

            // Purple group
            "RA/SLE": { base: "bg-fuchsia-50 text-fuchsia-800 border-fuchsia-200 hover:bg-fuchsia-100", selected: "bg-fuchsia-500 text-white border-fuchsia-500 hover:bg-fuchsia-600" },

            // Gray group
            RTA: categoryStyles.Other,
            ORTHO: categoryStyles.Other,
            POF: categoryStyles.Other,
            Smoker: categoryStyles.Other,
          }

          // Flatten all diseases with their category
          const allDiseases: { disease: string; category: string }[] = []
          Object.entries(DISEASE_CATEGORIES).forEach(([category, diseases]) => {
            diseases.forEach((disease) => {
              allDiseases.push({ disease, category })
            })
          })

          return (
            <div className="flex flex-wrap gap-2">
              {allDiseases.map(({ disease, category }) => {
                const style = diseaseStyles[disease] ?? categoryStyles[category]
                const isSelected = selectedDiseases.includes(disease)
                const classNames = isSelected ? style.selected : style.base
                return (
                  <Badge
                    key={disease}
                    variant="outline"
                    className={`cursor-pointer select-none transition-colors ${classNames}`}
                    onClick={() => toggleDisease(disease)}
                    title={category}
                  >
                    {disease}
                  </Badge>
                )
              })}
            </div>
          )
        })()}
      </div>

      <div className="space-y-2">
        <h4 className="text-sm font-medium">Add Disease:</h4>
        <div className="flex gap-2">
          <Input
            placeholder="Type disease name and press comma (,) to add (e.g., Hypertension,)"
            value={customInput}
            onChange={(e) => setCustomInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault()
                addCustomDiseases()
              }
            }}
          />
          <Button onClick={addCustomDiseases} size="sm">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">
          Type a disease name followed by a comma (,) to add it to the patient's history
        </p>
      </div>

      {selectedDiseases.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Selected Diseases:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedDiseases.map((disease) => (
              <Badge key={disease} variant="default" className="gap-1">
                {disease}
                <X className="h-3 w-3 cursor-pointer" onClick={() => removeDisease(disease)} />
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
