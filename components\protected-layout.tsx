"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { getSession } from "@/lib/auth"
import { AppHeader } from "./app-header"

interface ProtectedLayoutProps {
  children: React.ReactNode
  requireAdmin?: boolean
}

export function ProtectedLayout({ children, requireAdmin = false }: ProtectedLayoutProps) {
  const router = useRouter()

  useEffect(() => {
    const session = getSession()

    if (!session) {
      router.push("/login")
      return
    }

    if (requireAdmin && session.user.role !== "admin") {
      router.push("/")
      return
    }
  }, [router, requireAdmin])

  const session = getSession()
  if (!session) return null
  if (requireAdmin && session.user.role !== "admin") return null

  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <main className="container mx-auto px-4 py-6">{children}</main>
    </div>
  )
}
