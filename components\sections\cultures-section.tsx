"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Culture } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface CulturesSectionProps {
  patientId: string
}

export function CulturesSection({ patientId }: CulturesSectionProps) {
  const [cultures, setCultures] = useState<Culture[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCulture, setEditingCulture] = useState<Culture | null>(null)
  const [formData, setFormData] = useState({
    requested_date: new Date().toISOString().split("T")[0],
    result_date: "",
    culture_type: "",
    results: "",
    status: "pending" as "pending" | "positive" | "negative",
  })
  const { toast } = useToast()

  useEffect(() => {
    loadCultures()
  }, [patientId])

  const loadCultures = async () => {
    try {
      const culturesData = await db.cultures.where("patient_id").equals(patientId).toArray()

      // Sort by requested date in descending order (most recent first)
      culturesData.sort((a, b) => new Date(b.requested_date).getTime() - new Date(a.requested_date).getTime())

      setCultures(culturesData)
    } catch (error) {
      console.error("Error loading cultures:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      requested_date: new Date().toISOString().split("T")[0],
      result_date: "",
      culture_type: "",
      results: "",
      status: "pending",
    })
    setEditingCulture(null)
  }

  const handleEdit = (culture: Culture) => {
    setEditingCulture(culture)
    setFormData({
      requested_date: culture.requested_date,
      result_date: culture.result_date || "",
      culture_type: culture.culture_type || "",
      results: culture.results || "",
      status: culture.status,
    })
    setIsDialogOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const cultureData = {
        patient_id: patientId,
        requested_date: formData.requested_date,
        result_date: formData.result_date || undefined,
        culture_type: formData.culture_type,
        results: formData.results,
        status: formData.status,
      }

      if (editingCulture) {
        await dataClient.updateCulture(editingCulture.id, cultureData)
      } else {
        await dataClient.insertCulture(cultureData)
      }

      await loadCultures()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Culture saved",
        description: "Culture information has been successfully saved.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save culture information.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (culture: Culture) => {
    try {
      await dataClient.deleteCulture(culture.id)
      await loadCultures()

      toast({
        title: "Culture deleted",
        description: "Culture has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete culture.",
        variant: "destructive",
      })
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "positive":
        return "destructive"
      case "negative":
        return "secondary"
      default:
        return "outline"
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Cultures</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>{editingCulture ? "Edit Culture" : "Add Culture"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="requested_date">Requested Date</Label>
                  <Input
                    id="requested_date"
                    type="date"
                    value={formData.requested_date}
                    onChange={(e) => setFormData({ ...formData, requested_date: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="result_date">Result Date</Label>
                  <Input
                    id="result_date"
                    type="date"
                    placeholder="dd/mm/yyyy"
                    value={formData.result_date}
                    onChange={(e) => setFormData({ ...formData, result_date: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="culture_type">Culture Type / Specimen Source</Label>
                <Input
                  id="culture_type"
                  placeholder="e.g., Blood, Urine, Sputum"
                  value={formData.culture_type}
                  onChange={(e) => setFormData({ ...formData, culture_type: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="results">Culture Results</Label>
                <Textarea
                  id="results"
                  placeholder="Enter detailed results"
                  value={formData.results}
                  onChange={(e) => setFormData({ ...formData, results: e.target.value })}
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "pending" | "positive" | "negative") =>
                    setFormData({ ...formData, status: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="positive">Positive</SelectItem>
                    <SelectItem value="negative">Negative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">{editingCulture ? "Update Culture" : "Add Culture"}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {cultures.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No cultures recorded yet.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Requested Date</TableHead>
                <TableHead>Culture Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Result Date</TableHead>
                <TableHead>Results</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cultures.map((culture) => (
                <TableRow key={culture.id}>
                  <TableCell>{new Date(culture.requested_date).toLocaleDateString()}</TableCell>
                  <TableCell>{culture.culture_type || "-"}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(culture.status)}>
                      {culture.status.charAt(0).toUpperCase() + culture.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {culture.result_date ? new Date(culture.result_date).toLocaleDateString() : "-"}
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{culture.results || "-"}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleEdit(culture)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDelete(culture)}>
                        <Trash2 className="h-4 w-4 text-red-600" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
