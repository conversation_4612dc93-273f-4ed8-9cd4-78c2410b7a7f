"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, ChevronDown, ChevronRight } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Tit<PERSON>, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { dataClient } from "@/lib/data-client"
import type { LabValues } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"
import { db } from "@/lib/db"

interface LabsSectionProps {
  patientId: string
}

const LAB_CATEGORIES = {
  Electrolytes: {
    sodium: { name: "Sodium", unit: "mmol/L", normal: "135-145" },
    potassium: { name: "Potassium", unit: "mmol/L", normal: "3.5-5.0" },
    chloride: { name: "Chloride", unit: "mmol/L", normal: "98-107" },
    bicarbonate: { name: "Bicarbonate", unit: "mmol/L", normal: "22-28" },
  },
  Hematology: {
    wbc: { name: "WBC", unit: "×10⁹/L", normal: "4.0-11.0" },
    hemoglobin: { name: "Hemoglobin", unit: "g/dL", normal: "12-16" },
    hematocrit: { name: "Hematocrit", unit: "%", normal: "36-48" },
    platelets: { name: "Platelets", unit: "×10⁹/L", normal: "150-450" },
  },
  "Kidney Functions": {
    creatinine: { name: "Creatinine", unit: "mg/dL", normal: "0.6-1.2" },
    bun: { name: "BUN", unit: "mg/dL", normal: "7-20" },
    urea: { name: "Urea", unit: "mmol/L", normal: "2.5-7.5" },
  },
  "Liver Functions": {
    alt: { name: "ALT", unit: "U/L", normal: "7-56" },
    ast: { name: "AST", unit: "U/L", normal: "10-40" },
    bilirubin_total: { name: "Total Bilirubin", unit: "mg/dL", normal: "0.3-1.2" },
    albumin: { name: "Albumin", unit: "g/dL", normal: "3.5-5.0" },
  },
  "Inflammatory Markers": {
    crp: { name: "CRP", unit: "mg/L", normal: "<3.0" },
    esr: { name: "ESR", unit: "mm/hr", normal: "<30" },
    procalcitonin: { name: "Procalcitonin", unit: "ng/mL", normal: "<0.25" },
  },
  ABG: {
    ph: { name: "pH", unit: "", normal: "7.35-7.45" },
    pco2: { name: "PCO2", unit: "mmHg", normal: "35-45" },
    po2: { name: "PO2", unit: "mmHg", normal: "80-100" },
    hco3: { name: "HCO3", unit: "mmol/L", normal: "22-26" },
  },
  Cardiac: {
    troponin: { name: "Troponin", unit: "ng/mL", normal: "<0.04" },
    ck_mb: { name: "CK-MB", unit: "ng/mL", normal: "<6.3" },
    bnp: { name: "BNP", unit: "pg/mL", normal: "<100" },
  },
}

export function LabsSection({ patientId }: LabsSectionProps) {
  const [labs, setLabs] = useState<LabValues[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    lab_data: {} as Record<string, string>,
  })
  const [openCategories, setOpenCategories] = useState<Record<string, boolean>>({})
  const { toast } = useToast()

  useEffect(() => {
    loadLabs()
  }, [patientId])

  const loadLabs = async () => {
    try {
      const labsData = await db.lab_values.where("patient_id").equals(patientId).toArray()

      // Sort by date in descending order (most recent first)
      labsData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      setLabs(labsData)
    } catch (error) {
      console.error("Error loading labs:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      lab_data: {},
    })
  }

  const toggleCategory = (category: string) => {
    setOpenCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  const handleLabValueChange = (key: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      lab_data: {
        ...prev.lab_data,
        [key]: value,
      },
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      // Filter out empty values
      const filteredLabData = Object.fromEntries(
        Object.entries(formData.lab_data).filter(([_, value]) => value.trim() !== ""),
      )

      await dataClient.upsertLabValues({
        patient_id: patientId,
        date: formData.date,
        lab_data: filteredLabData,
      })

      await loadLabs()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Lab results saved",
        description: "Lab values have been successfully recorded.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save lab results.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Lab Values</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add Lab Values</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-4">
                {Object.entries(LAB_CATEGORIES).map(([category, tests]) => (
                  <Collapsible
                    key={category}
                    open={openCategories[category]}
                    onOpenChange={() => toggleCategory(category)}
                  >
                    <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-muted rounded-lg hover:bg-muted/80">
                      <span className="font-medium">{category}</span>
                      {openCategories[category] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2 space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(tests).map(([key, test]) => (
                          <div key={key} className="space-y-1">
                            <Label htmlFor={key} className="text-sm">
                              {test.name} ({test.unit})
                              <span className="text-xs text-muted-foreground ml-1">Normal: {test.normal}</span>
                            </Label>
                            <Input
                              id={key}
                              type="number"
                              step="0.01"
                              placeholder={`Enter ${test.name.toLowerCase()}`}
                              value={formData.lab_data[key] || ""}
                              onChange={(e) => handleLabValueChange(key, e.target.value)}
                            />
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Lab Results</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {labs.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No lab results recorded yet.</div>
        ) : (
          <div className="space-y-4">
            {labs.map((lab) => (
              <Card key={lab.id} className="p-4">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold">{new Date(lab.date).toLocaleDateString()}</h4>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {Object.entries(lab.lab_data).map(([key, value]) => {
                    // Find the test info
                    let testInfo = null
                    for (const [category, tests] of Object.entries(LAB_CATEGORIES)) {
                      if (tests[key as keyof typeof tests]) {
                        testInfo = tests[key as keyof typeof tests]
                        break
                      }
                    }

                    return (
                      <div key={key} className="text-sm">
                        <div className="font-medium">{testInfo?.name || key}</div>
                        <div className="text-muted-foreground">
                          {value} {testInfo?.unit}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
