"use client"

import type React from "react"
import { db } from "@/lib/db" // Declare the db variable here
import { useState, useEffect } from "react"
import { Plus, Search, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { dataClient } from "@/lib/data-client"
import type { Medication } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"

interface MedicationsSectionProps {
  patientId: string
}

export function MedicationsSection({ patientId }: MedicationsSectionProps) {
  const [medications, setMedications] = useState<Medication[]>([])
  const [medicationNames, setMedicationNames] = useState<string[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [newMedicationName, setNewMedicationName] = useState("")
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split("T")[0],
    medication_name: "",
    dosage: "",
    frequency: "",
    route: "",
  })
  const { toast } = useToast()

  useEffect(() => {
    loadMedications()
    loadMedicationNames()
  }, [patientId])

  const loadMedications = async () => {
    try {
      const medicationsData = await db.medications.where("patient_id").equals(patientId).toArray()

      // Sort by date prescribed in descending order (most recent first)
      medicationsData.sort((a, b) => new Date(b.date_prescribed).getTime() - new Date(a.date_prescribed).getTime())

      setMedications(medicationsData)
    } catch (error) {
      console.error("Error loading medications:", error)
    }
  }

  const loadMedicationNames = async () => {
    try {
      const names = await dataClient.getMedicationNames()
      setMedicationNames(names)
    } catch (error) {
      console.error("Error loading medication names:", error)
    }
  }

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split("T")[0],
      medication_name: "",
      dosage: "",
      frequency: "",
      route: "",
    })
    setSearchTerm("")
    setNewMedicationName("")
  }

  const filteredMedicationNames = medicationNames.filter((name) =>
    name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleAddNewMedication = async () => {
    if (!newMedicationName.trim()) return

    try {
      await dataClient.insertMedicationName(newMedicationName.trim())
      await loadMedicationNames()
      setFormData({ ...formData, medication_name: newMedicationName.trim() })
      setNewMedicationName("")
      toast({
        title: "Medication added",
        description: "New medication has been added to the catalog.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add new medication to catalog.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.medication_name) {
      toast({
        title: "Error",
        description: "Please select or add a medication name.",
        variant: "destructive",
      })
      return
    }

    try {
      await dataClient.insertMedication({
        patient_id: patientId,
        medication_name: formData.medication_name,
        dosage: formData.dosage,
        frequency: formData.frequency,
        route: formData.route,
        date_prescribed: formData.date,
        is_active: true,
      })

      await loadMedications()
      setIsDialogOpen(false)
      resetForm()

      toast({
        title: "Medication added",
        description: "Medication has been successfully added.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add medication.",
        variant: "destructive",
      })
    }
  }

  const handleToggleActive = async (medication: Medication) => {
    try {
      await dataClient.updateMedication(medication.id, {
        is_active: !medication.is_active,
      })
      await loadMedications()

      toast({
        title: "Medication updated",
        description: `Medication ${medication.is_active ? "deactivated" : "activated"}.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update medication status.",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (medication: Medication) => {
    try {
      await dataClient.deleteMedication(medication.id)
      await loadMedications()

      toast({
        title: "Medication deleted",
        description: "Medication has been removed.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete medication.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Medications</CardTitle>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Add Medication</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label>Search existing medication</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search and select medication"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                {searchTerm && filteredMedicationNames.length > 0 && (
                  <div className="border rounded-md max-h-32 overflow-y-auto">
                    {filteredMedicationNames.slice(0, 5).map((name) => (
                      <div
                        key={name}
                        className="p-2 hover:bg-muted cursor-pointer"
                        onClick={() => {
                          setFormData({ ...formData, medication_name: name })
                          setSearchTerm("")
                        }}
                      >
                        {name}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Add new medication to catalog</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter a new medication name"
                    value={newMedicationName}
                    onChange={(e) => setNewMedicationName(e.target.value)}
                  />
                  <Button type="button" onClick={handleAddNewMedication}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {formData.medication_name && (
                <div className="p-2 bg-muted rounded">
                  Selected: <strong>{formData.medication_name}</strong>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="dosage">Dosage</Label>
                <Input
                  id="dosage"
                  placeholder="e.g., 500mg"
                  value={formData.dosage}
                  onChange={(e) => setFormData({ ...formData, dosage: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Select onValueChange={(value) => setFormData({ ...formData, frequency: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Once daily">Once daily</SelectItem>
                    <SelectItem value="Twice daily">Twice daily</SelectItem>
                    <SelectItem value="Three times daily">Three times daily</SelectItem>
                    <SelectItem value="Four times daily">Four times daily</SelectItem>
                    <SelectItem value="Every 4 hours">Every 4 hours</SelectItem>
                    <SelectItem value="Every 6 hours">Every 6 hours</SelectItem>
                    <SelectItem value="Every 8 hours">Every 8 hours</SelectItem>
                    <SelectItem value="Every 12 hours">Every 12 hours</SelectItem>
                    <SelectItem value="As needed">As needed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="route">Route</Label>
                <Select onValueChange={(value) => setFormData({ ...formData, route: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select route" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Oral">Oral</SelectItem>
                    <SelectItem value="IV">IV</SelectItem>
                    <SelectItem value="IM">IM</SelectItem>
                    <SelectItem value="SC">SC</SelectItem>
                    <SelectItem value="Topical">Topical</SelectItem>
                    <SelectItem value="Inhalation">Inhalation</SelectItem>
                    <SelectItem value="Rectal">Rectal</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Medication</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {medications.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">No medications recorded yet.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Medication</TableHead>
                <TableHead>Dosage</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Route</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date Prescribed</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {medications.map((medication) => (
                <TableRow key={medication.id}>
                  <TableCell className="font-medium">{medication.medication_name}</TableCell>
                  <TableCell>{medication.dosage || "-"}</TableCell>
                  <TableCell>{medication.frequency || "-"}</TableCell>
                  <TableCell>{medication.route || "-"}</TableCell>
                  <TableCell>
                    <Badge
                      variant={medication.is_active ? "default" : "secondary"}
                      className="cursor-pointer"
                      onClick={() => handleToggleActive(medication)}
                    >
                      {medication.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(medication.date_prescribed).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(medication)}>
                      <Trash2 className="h-4 w-4 text-red-600" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
