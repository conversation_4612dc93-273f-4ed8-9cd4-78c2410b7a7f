"use client"

import <PERSON><PERSON>, { type Table } from "dexie"
import type {
  Patient,
  VitalSigns,
  LabValues,
  Medication,
  MedicationName,
  DoctorNote,
  Culture,
  Radiology,
  OutboxOperation,
  IdMapping,
} from "./types"

export class ICUDatabase extends Dexie {
  patients!: Table<Patient>
  vital_signs!: Table<VitalSigns>
  lab_values!: Table<LabValues>
  medications!: Table<Medication>
  medication_names!: Table<MedicationName>
  doctor_notes!: Table<DoctorNote>
  cultures!: Table<Culture>
  radiology!: Table<Radiology>
  outbox!: Table<OutboxOperation>
  id_mappings!: Table<IdMapping>

  constructor() {
    super("ICUDatabase")
    this.version(1).stores({
      patients: "id, patient_id, name, admission_date, created_at",
      vital_signs: "id, patient_id, date, created_at, [patient_id+date]",
      lab_values: "id, patient_id, date, created_at, [patient_id+date]",
      medications: "id, patient_id, medication_name, date_prescribed, is_active",
      medication_names: "id, name",
      doctor_notes: "id, patient_id, date, created_at",
      cultures: "id, patient_id, requested_date, status",
      radiology: "id, patient_id, scan_date, scan_type, status",
      outbox: "id, table_name, operation, created_at, retries",
      id_mappings: "local_id, server_id, table_name",
    })
  }
}

export const db = new ICUDatabase()
