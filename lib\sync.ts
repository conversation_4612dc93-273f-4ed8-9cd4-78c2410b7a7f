"use client"

import { db } from "./db"
import { supabase } from "./supabase"
import { dataClient } from "./data-client"
import type { OutboxOperation } from "./types"

class SyncService {
  private isOnline = typeof navigator !== "undefined" ? navigator.onLine : false
  private syncInterval: NodeJS.Timeout | null = null

  constructor() {
    if (typeof window !== "undefined") {
      window.addEventListener("online", () => {
        this.isOnline = true
        this.startPeriodicSync()
      })

      window.addEventListener("offline", () => {
        this.isOnline = false
        this.stopPeriodicSync()
      })

      if (this.isOnline) {
        this.startPeriodicSync()
      }
    }
  }

  startPeriodicSync() {
    if (this.syncInterval) return

    this.syncInterval = setInterval(() => {
      this.syncOutbox()
    }, 30000) // 30 seconds

    // Initial sync
    this.syncOutbox()
  }

  stopPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
    }
  }

  async syncOutbox() {
    if (!this.isOnline) return

    const operations = await dataClient.getOutboxOperations()

    for (const operation of operations) {
      try {
        await this.processOperation(operation)
        await dataClient.deleteOutboxOperation(operation.id)
      } catch (error) {
        console.error("Sync error:", error)
        await dataClient.updateOutboxOperation(operation.id, {
          retries: operation.retries + 1,
          error: error instanceof Error ? error.message : "Unknown error",
        })
      }
    }
  }

  private async processOperation(operation: OutboxOperation) {
    const { table_name, operation: op, data, local_id } = operation

    switch (op) {
      case "insert":
        const { data: insertResult, error: insertError } = await supabase
          .from(table_name)
          .insert(data)
          .select()
          .single()

        if (insertError) throw insertError

        // Map local ID to server ID
        if (local_id && insertResult) {
          await db.id_mappings.add({
            local_id,
            server_id: insertResult.id,
            table_name,
          })
        }
        break

      case "update":
        const { error: updateError } = await supabase.from(table_name).update(data).eq("id", data.id)

        if (updateError) throw updateError
        break

      case "delete":
        const { error: deleteError } = await supabase.from(table_name).delete().eq("id", data.id)

        if (deleteError) throw deleteError
        break
    }
  }

  async pullServerData() {
    if (!this.isOnline) return

    try {
      // Pull all tables
      const tables = [
        "patients",
        "vital_signs",
        "lab_values",
        "medications",
        "medication_names",
        "doctor_notes",
        "cultures",
        "radiology",
      ]

      for (const tableName of tables) {
        const { data, error } = await supabase.from(tableName).select("*")
        if (error) throw error

        if (data && data.length > 0) {
          // @ts-ignore - Dynamic table access
          await db[tableName].bulkPut(data)
        }
      }
    } catch (error) {
      console.error("Pull sync error:", error)
    }
  }
}

export const syncService = new SyncService()
