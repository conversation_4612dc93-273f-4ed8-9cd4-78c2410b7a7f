export interface User {
  id: string
  username: string
  role: "admin" | "user"
}

export interface Patient {
  id: string
  patient_id: string
  name: string
  gender: "Male" | "Female" | "Other"
  age: number
  weight: number
  admission_date: string
  unit?: "ICU A" | "ICU B"
  main_complaint?: string
  medical_history?: string
  initial_diagnosis?: string
  is_active?: boolean
  is_discharged?: boolean
  is_deceased?: boolean
  diseases: string[]
  created_at: string
  updated_at: string
}

export interface VitalSigns {
  id: string
  patient_id: string
  date: string
  heart_rate?: number
  systolic_bp?: number
  diastolic_bp?: number
  temperature?: number
  respiratory_rate?: number
  oxygen_saturation?: number
  cvp?: number
  fluid_balance?: number
  fluid_intake?: number
  fluid_output?: number
  sofa_score?: number
  apache_score?: number
  rifle_score?: number
  on_ventilator: boolean
  on_support: boolean
  on_dialysis: boolean
  created_at: string
}

export interface LabValues {
  id: string
  patient_id: string
  date: string
  lab_data: Record<string, any>
  created_at: string
}

export interface Medication {
  id: string
  patient_id: string
  medication_name: string
  dosage?: string
  frequency?: string
  route?: string
  date_prescribed: string
  is_active: boolean
  created_at: string
}

export interface MedicationName {
  id: string
  name: string
  created_at: string
}

export interface DoctorNote {
  id: string
  patient_id: string
  date: string
  content: string
  doctor_name?: string
  created_at: string
}

export interface Culture {
  id: string
  patient_id: string
  requested_date: string
  result_date?: string
  culture_type?: string
  results?: string
  status: "pending" | "positive" | "negative"
  created_at: string
}

export interface Radiology {
  id: string
  patient_id: string
  scan_type: "X-ray" | "CT" | "MRI" | "Ultrasound"
  scan_date: string
  body_part?: string
  findings?: string
  status: "scheduled" | "completed" | "cancelled"
  created_at: string
}

export interface OutboxOperation {
  id: string
  table_name: string
  operation: "insert" | "update" | "delete"
  data: any
  local_id?: string
  server_id?: string
  created_at: string
  retries: number
  error?: string
}

export interface IdMapping {
  local_id: string
  server_id: string
  table_name: string
}
