-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate enum types
CREATE TYPE user_role AS ENUM ('admin', 'user');
CREATE TYPE culture_status AS ENUM ('pending', 'positive', 'negative');
CREATE TYPE radiology_scan_type AS ENUM ('X-ray', 'CT', 'MRI', 'Ultrasound');
CREATE TYPE radiology_status AS ENUM ('scheduled', 'completed', 'cancelled');
CREATE TYPE gender_type AS ENUM ('Male', 'Female', 'Other');
CREATE TYPE unit_type AS ENUM ('ICU A', 'ICU B');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role user_role DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Patients table
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender gender_type NOT NULL,
    age INTEGER CHECK (age >= 1 AND age <= 150),
    weight DECIMAL(5,2) CHECK (weight > 0),
    admission_date DATE DEFAULT CURRENT_DATE,
    unit unit_type,
    main_complaint TEXT,
    medical_history TEXT,
    initial_diagnosis TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_discharged BOOLEAN DEFAULT FALSE,
    is_deceased BOOLEAN DEFAULT FALSE,
    diseases JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Vital signs table
CREATE TABLE vital_signs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    heart_rate INTEGER,
    systolic_bp INTEGER,
    diastolic_bp INTEGER,
    temperature DECIMAL(4,1),
    respiratory_rate INTEGER,
    oxygen_saturation INTEGER,
    cvp INTEGER,
    fluid_balance INTEGER,
    fluid_intake INTEGER,
    fluid_output INTEGER,
    sofa_score INTEGER,
    apache_score INTEGER,
    rifle_score INTEGER,
    on_ventilator BOOLEAN DEFAULT FALSE,
    on_support BOOLEAN DEFAULT FALSE,
    on_dialysis BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(patient_id, date)
);

-- Lab values table
CREATE TABLE lab_values (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE NOT NULL,
    lab_data JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(patient_id, date)
);

-- Medication names catalog
CREATE TABLE medication_names (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Medications table
CREATE TABLE medications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    medication_name VARCHAR(100) NOT NULL,
    dosage VARCHAR(50),
    frequency VARCHAR(50),
    route VARCHAR(50),
    date_prescribed DATE DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Doctor notes table
CREATE TABLE doctor_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    date DATE DEFAULT CURRENT_DATE,
    content TEXT NOT NULL,
    doctor_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Cultures table
CREATE TABLE cultures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    requested_date DATE NOT NULL,
    result_date DATE,
    culture_type VARCHAR(100),
    results TEXT,
    status culture_status DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Radiology table
CREATE TABLE radiology (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) REFERENCES patients(patient_id) ON DELETE CASCADE,
    scan_type radiology_scan_type NOT NULL,
    scan_date DATE NOT NULL,
    body_part VARCHAR(100),
    findings TEXT,
    status radiology_status DEFAULT 'scheduled',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;
ALTER TABLE vital_signs ENABLE ROW LEVEL SECURITY;
ALTER TABLE lab_values ENABLE ROW LEVEL SECURITY;
ALTER TABLE medications ENABLE ROW LEVEL SECURITY;
ALTER TABLE medication_names ENABLE ROW LEVEL SECURITY;
ALTER TABLE doctor_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE cultures ENABLE ROW LEVEL SECURITY;
ALTER TABLE radiology ENABLE ROW LEVEL SECURITY;

-- Create permissive policies (allow all operations for authenticated users)
CREATE POLICY "Allow all operations" ON patients FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON vital_signs FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON lab_values FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON medications FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON medication_names FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON doctor_notes FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON cultures FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON radiology FOR ALL USING (true);

-- Create login function
CREATE OR REPLACE FUNCTION login(username_input TEXT, password_input TEXT)
RETURNS JSON AS $$
DECLARE
    user_record RECORD;
    is_valid BOOLEAN;
BEGIN
    SELECT * INTO user_record FROM users WHERE username = username_input;
    
    IF user_record IS NULL THEN
        RETURN json_build_object('success', false, 'message', 'Invalid credentials');
    END IF;
    
    -- In production, use proper password hashing
    is_valid := user_record.password_hash = crypt(password_input, user_record.password_hash);
    
    IF is_valid THEN
        RETURN json_build_object(
            'success', true,
            'user', json_build_object(
                'id', user_record.id,
                'username', user_record.username,
                'role', user_record.role
            )
        );
    ELSE
        RETURN json_build_object('success', false, 'message', 'Invalid credentials');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create user function for admin
CREATE OR REPLACE FUNCTION create_user(
    username_input TEXT,
    password_input TEXT,
    role_input user_role DEFAULT 'user'
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
BEGIN
    -- Check if username already exists
    IF EXISTS (SELECT 1 FROM users WHERE username = username_input) THEN
        RETURN json_build_object('success', false, 'message', 'Username already exists');
    END IF;
    
    -- Insert new user
    INSERT INTO users (username, password_hash, role)
    VALUES (username_input, crypt(password_input, gen_salt('bf')), role_input)
    RETURNING id INTO new_user_id;
    
    RETURN json_build_object(
        'success', true,
        'message', 'User created successfully',
        'user_id', new_user_id
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object('success', false, 'message', 'Failed to create user: ' || SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update user password function
CREATE OR REPLACE FUNCTION update_user_password(
    user_id UUID,
    new_password TEXT
)
RETURNS JSON AS $$
BEGIN
    UPDATE users 
    SET password_hash = crypt(new_password, gen_salt('bf'))
    WHERE id = user_id;
    
    IF FOUND THEN
        RETURN json_build_object('success', true, 'message', 'Password updated successfully');
    ELSE
        RETURN json_build_object('success', false, 'message', 'User not found');
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object('success', false, 'message', 'Failed to update password: ' || SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION login(TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_user(TEXT, TEXT, user_role) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_user_password(UUID, TEXT) TO anon, authenticated;

-- Seed admin user (password: admin123)
INSERT INTO users (username, password_hash, role) 
VALUES ('admin', crypt('admin123', gen_salt('bf')), 'admin')
ON CONFLICT (username) DO NOTHING;

-- Seed some medication names
INSERT INTO medication_names (name) VALUES 
('Paracetamol'), ('Ibuprofen'), ('Aspirin'), ('Metformin'), ('Lisinopril'),
('Amlodipine'), ('Simvastatin'), ('Omeprazole'), ('Salbutamol'), ('Prednisolone')
ON CONFLICT (name) DO NOTHING;
